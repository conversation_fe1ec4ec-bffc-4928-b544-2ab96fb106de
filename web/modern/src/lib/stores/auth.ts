import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: number
  username: string
  display_name?: string
  role: number
  status: number
  email?: string
  quota: number
  used_quota: number
  group: string
}

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isValidating: boolean
  login: (user: User, token: string) => void
  logout: () => void
  updateUser: (user: Partial<User>) => void
  validateSession: () => Promise<boolean>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isValidating: false,
      login: (user, token) => {
        localStorage.setItem('token', token)
        localStorage.setItem('user', JSON.stringify(user))
        set({ user, token, isAuthenticated: true })
      },
      logout: () => {
        localStorage.removeItem('token')
        localStorage.removeItem('user')
        set({ user: null, token: null, isAuthenticated: false })
      },
      updateUser: (userData) => {
        const currentUser = get().user
        if (currentUser) {
          const updatedUser = { ...currentUser, ...userData }
          localStorage.setItem('user', JSON.stringify(updatedUser))
          set({ user: updatedUser })
        }
      },
      validateSession: async () => {
        const { user } = get()
        if (!user) return false

        set({ isValidating: true })

        try {
          // Import api here to avoid circular dependency
          const { api } = await import('../api')

          // Make a simple API call to validate the session
          const response = await api.get('/api/user/self')

          if (response.data.success) {
            set({ isValidating: false })
            return true
          } else {
            // Session is invalid, clear auth data
            localStorage.removeItem('token')
            localStorage.removeItem('user')
            set({ user: null, token: null, isAuthenticated: false, isValidating: false })
            return false
          }
        } catch (error) {
          // Session validation failed, clear auth data
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          set({ user: null, token: null, isAuthenticated: false, isValidating: false })
          return false
        }
      },
    }),
    {
      name: 'auth-storage',
    }
  )
)
