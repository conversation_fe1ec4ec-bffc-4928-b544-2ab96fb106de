import { Navigate, Outlet, useLocation } from 'react-router-dom'
import { useAuthStore } from '@/lib/stores/auth'
import { useEffect, useState } from 'react'

export function ProtectedRoute() {
  const { user, isValidating, validateSession } = useAuthStore()
  const location = useLocation()
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    const initializeAuth = async () => {
      if (user) {
        // If user exists in store, validate the session
        await validateSession()
      }
      setIsInitialized(true)
    }

    initializeAuth()
  }, [user, validateSession])

  // Show loading state while initializing or validating
  if (!isInitialized || isValidating) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Redirect to login with current path as redirect_to parameter
  if (!user) {
    const redirectTo = encodeURIComponent(location.pathname + location.search)
    return <Navigate to={`/login?redirect_to=${redirectTo}`} replace />
  }

  return <Outlet />
}
