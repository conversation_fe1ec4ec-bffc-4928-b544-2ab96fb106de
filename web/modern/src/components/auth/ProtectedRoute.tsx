import { Navigate, Outlet, useLocation } from 'react-router-dom'
import { useAuthStore } from '@/lib/stores/auth'
import { useEffect, useState } from 'react'

export function ProtectedRoute() {
  const { user } = useAuthStore()
  const location = useLocation()
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    // Give the store time to hydrate from localStorage
    const timer = setTimeout(() => {
      setIsHydrated(true)
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  // Show loading state while store is hydrating
  if (!isHydrated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Redirect to login with current path as redirect_to parameter
  if (!user) {
    const redirectTo = encodeURIComponent(location.pathname + location.search)
    return <Navigate to={`/login?redirect_to=${redirectTo}`} replace />
  }

  return <Outlet />
}
